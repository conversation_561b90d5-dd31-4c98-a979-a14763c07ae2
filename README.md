# SantriMental - Platform Skrining Kesehatan Mental

Platform modern untuk skrining kesehatan mental dengan berbagai jenis assessment yang dapat dikelola secara dinamis.

## 🌟 Fitur Utama

### 📋 Sistem Assessment Dinamis
- **SRQ-20** (Self Report Questionnaire 20) - Skrining kesehatan mental umum ✅
- **GSE** (General Self-Efficacy Scale) - Mengukur keyakinan diri ✅
- **MSCS** (Multidimensional Scale of Perceived Social Support) - Dukungan sosial ✅
- **MHKQ** (Mental Health Knowledge Questionnaire) - Pengetahuan kesehatan mental ✅
- **DASS42** (Depression, Anxiety and Stress Scale) - <PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON>, dan stres ✅

### 🔐 Autentikasi Real-time
- Registrasi dan login dengan validasi
- Session management dengan Laravel Sanctum
- Google OAuth integration (ready)
- QR Code login (ready)

### 📊 Dashboard & Analytics
- Statistik assessment real-time
- Grafik tren skor dari waktu ke waktu
- Riwayat lengkap semua assessment
- Filter dan pencarian hasil

### 🎨 User Interface Modern
- Glass morphism design
- Responsive untuk semua perangkat
- Animasi dan transisi yang smooth
- Dark theme dengan gradient background

## 🛠 Teknologi

### Backend
- **Laravel 10** - PHP Framework
- **MySQL** - Database
- **Laravel Sanctum** - API Authentication
- **RESTful API** - Clean API architecture

### Frontend
- **Vanilla JavaScript** - No framework dependencies
- **Tailwind CSS** - Utility-first CSS
- **Chart.js** - Data visualization
- **Modern ES6+** - Clean JavaScript code

## 📁 Struktur Proyek

```
santrimental/
├── backend/                 # Laravel API Backend
│   ├── app/
│   │   ├── Http/Controllers/Api/
│   │   │   ├── AuthController.php
│   │   │   ├── FormTemplateController.php
│   │   │   └── AssessmentController.php
│   │   └── Models/
│   │       ├── User.php
│   │       ├── FormTemplate.php
│   │       ├── FormQuestion.php
│   │       └── FormResponse.php
│   ├── database/
│   │   ├── migrations/
│   │   └── seeders/
│   ├── resources/views/     # Blade Templates
│   └── routes/api.php
├── js/                      # Frontend JavaScript
│   ├── auth.js
│   ├── utils.js
│   ├── dashboard.js
│   ├── dynamic-form.js
│   └── history.js
├── css/                     # Stylesheets
├── index.html              # Landing page (legacy)
├── dashboard.html          # Dashboard (legacy)
└── README.md
```

## 🚀 Instalasi & Setup

### Prerequisites
- PHP 8.1+
- Composer
- MySQL 8.0+
- Node.js 16+ (optional)

### 1. Clone Repository
```bash
git clone <repository-url>
cd santrimental
```

### 2. Setup Backend
```bash
cd backend
composer install
cp .env.example .env
php artisan key:generate
```

### 3. Konfigurasi Database
Edit file `.env`:
```env
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=santrimental
DB_USERNAME=your_username
DB_PASSWORD=your_password
```

### 4. Migrasi & Seeding
```bash
php artisan migrate
php artisan db:seed --class=UserSeeder
php artisan db:seed --class=FormTemplateSeeder
```

### 5. Jalankan Server
```bash
php artisan serve
```

Aplikasi akan berjalan di `http://127.0.0.1:8000`

## 👤 Demo Credentials

### User Demo
- **Email**: `<EMAIL>`
- **Password**: `demo123`

### User Test
- **Email**: `<EMAIL>`
- **Password**: `password`

## 📖 Penggunaan

### 1. Login
- Buka `http://127.0.0.1:8000`
- Login dengan credentials demo
- Atau daftar akun baru

### 2. Pilih Assessment
- Dari dashboard, klik "Assessment"
- Pilih jenis assessment yang diinginkan
- Baca instruksi dengan teliti

### 3. Isi Assessment
- Jawab semua pertanyaan dengan jujur
- Progress bar menunjukkan kemajuan
- Klik "Lihat Hasil" setelah selesai

### 4. Lihat Hasil
- Skor dan interpretasi ditampilkan
- Rekomendasi tindak lanjut
- Simpan hasil untuk tracking

### 5. Riwayat Assessment
- Akses melalui menu "Riwayat"
- Filter berdasarkan status atau periode
- Lihat tren perkembangan

## 🔧 Konfigurasi Form Dinamis

### Jenis Scoring yang Didukung
Sistem mendukung 4 jenis scoring:
- **Binary Sum**: Ya/Tidak (SRQ-20)
- **Likert Sum**: Skala 1-4, 1-7 (GSE, MSCS)
- **Knowledge Sum**: Benar/Salah (MHKQ)
- **DASS Scale**: Skala 0-3 dengan subscales (DASS42)

### Menambah Assessment Baru
1. Tambahkan konfigurasi ke `FormTemplateSeeder`
2. Jalankan `php artisan db:seed --class=FormTemplateSeeder`
3. Form baru akan otomatis muncul di halaman assessment

Lihat `FORM_INTEGRATION_GUIDE.md` untuk panduan lengkap.

### Format Konfigurasi
```json
{
  "questions": ["Pertanyaan 1", "Pertanyaan 2"],
  "scoring_rules": {
    "type": "binary_sum",
    "max_score": 20,
    "questions": {
      "1": {"type": "binary", "yes_score": 1, "no_score": 0}
    }
  },
  "interpretation_rules": [
    {
      "min_score": 0,
      "max_score": 5,
      "status": "normal",
      "interpretation": "Kondisi baik",
      "recommendations": ["Saran 1", "Saran 2"]
    }
  ]
}
```

## 🔌 API Endpoints

### Authentication
- `POST /api/login` - User login
- `POST /api/register` - User registration
- `POST /api/logout` - User logout

### Forms
- `GET /api/forms` - List semua form
- `GET /api/forms/{code}` - Detail form
- `POST /api/forms/{code}/submit` - Submit jawaban
- `GET /api/forms/{code}/responses` - Riwayat user

### Dashboard
- `GET /api/dashboard/stats` - Statistik dashboard

## 🧪 Testing

### Manual Testing
1. Test registrasi user baru
2. Test login dengan berbagai credentials
3. Test setiap jenis assessment
4. Test penyimpanan dan retrieval data
5. Test dashboard dan riwayat

### API Testing
```bash
# Test login
curl -X POST http://127.0.0.1:8000/api/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"demo123"}'

# Test get forms
curl -X GET http://127.0.0.1:8000/api/forms \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## 🚀 Deployment

### Production Setup
1. Set environment ke production
2. Konfigurasi database production
3. Setup SSL certificate
4. Configure web server (Nginx/Apache)
5. Setup queue workers untuk background jobs

### Environment Variables
```env
APP_ENV=production
APP_DEBUG=false
APP_URL=https://yourdomain.com
```

## 🤝 Contributing

1. Fork repository
2. Buat feature branch
3. Commit changes
4. Push ke branch
5. Create Pull Request

## 📄 License

This project is licensed under the MIT License.

## 📞 Support

Untuk pertanyaan atau dukungan:
- Email: <EMAIL>
- Documentation: [Link to docs]
- Issues: [GitHub Issues]

## 🔄 Changelog

### v1.0.0 (Current)
- ✅ Sistem authentication real-time
- ✅ Form dinamis untuk multiple assessment
- ✅ Dashboard dengan analytics
- ✅ Riwayat assessment lengkap
- ✅ UI/UX modern dan responsive

### Roadmap
- [ ] Mobile app integration
- [ ] Advanced analytics
- [ ] Multi-language support
- [ ] Export hasil ke PDF
- [ ] Notifikasi reminder assessment
