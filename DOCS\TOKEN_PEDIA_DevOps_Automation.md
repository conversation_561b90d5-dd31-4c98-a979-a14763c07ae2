# 🚀 TOKEN PEDIA - DEVOPS & AUTOMATION
## Automated Deployment & Infrastructure Management

---

## 🎯 DEVOPS STRATEGY

### **Automation Goals:**
- ✅ **Zero-Downtime Deployment**: Blue-green deployment strategy
- ✅ **Infrastructure as Code**: Terraform/CloudFormation
- ✅ **Automated Testing**: CI/CD pipeline with comprehensive testing
- ✅ **Monitoring & Alerting**: Real-time system health monitoring
- ✅ **Scalable Architecture**: Auto-scaling based on demand

### **Technology Stack:**
- **CI/CD**: GitHub Actions
- **Infrastructure**: AWS/GCP with Terraform
- **Containerization**: Docker & Docker Compose
- **Orchestration**: Kubernetes (optional for scale)
- **Monitoring**: Prometheus, Grafana, New Relic
- **Logging**: ELK Stack (Elasticsearch, Logstash, Kibana)

---

## 🏗️ INFRASTRUCTURE SETUP

### **1. DOCKER CONFIGURATION**

#### **Backend Dockerfile:**
```dockerfile
# Backend Dockerfile
FROM php:8.1-fpm-alpine

# Install system dependencies
RUN apk add --no-cache \
    git \
    curl \
    libpng-dev \
    libxml2-dev \
    zip \
    unzip \
    mysql-client \
    nodejs \
    npm

# Install PHP extensions
RUN docker-php-ext-install pdo pdo_mysql mbstring exif pcntl bcmath gd

# Install Composer
COPY --from=composer:latest /usr/bin/composer /usr/bin/composer

# Set working directory
WORKDIR /var/www/html

# Copy composer files
COPY composer.json composer.lock ./

# Install PHP dependencies
RUN composer install --no-dev --optimize-autoloader --no-interaction

# Copy application code
COPY . .

# Set permissions
RUN chown -R www-data:www-data /var/www/html \
    && chmod -R 755 /var/www/html/storage

# Generate application key
RUN php artisan key:generate --no-interaction

# Optimize Laravel
RUN php artisan config:cache \
    && php artisan route:cache \
    && php artisan view:cache

EXPOSE 9000

CMD ["php-fpm"]
```

#### **Frontend Dockerfile:**
```dockerfile
# Frontend Dockerfile
FROM node:18-alpine AS builder

WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci --only=production

# Copy source code
COPY . .

# Build application
RUN npm run build

# Production stage
FROM nginx:alpine

# Copy built assets
COPY --from=builder /app/dist /usr/share/nginx/html

# Copy nginx configuration
COPY nginx.conf /etc/nginx/nginx.conf

EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]
```

#### **Docker Compose (Development):**
```yaml
version: '3.8'

services:
  # Backend API
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: token-pedia-backend
    ports:
      - "8000:9000"
    volumes:
      - ./backend:/var/www/html
      - ./backend/storage:/var/www/html/storage
    environment:
      - APP_ENV=local
      - DB_HOST=mysql
      - DB_DATABASE=token_pedia
      - DB_USERNAME=root
      - DB_PASSWORD=password
      - REDIS_HOST=redis
    depends_on:
      - mysql
      - redis
    networks:
      - token-pedia-network

  # Frontend
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: token-pedia-frontend
    ports:
      - "3000:80"
    depends_on:
      - backend
    networks:
      - token-pedia-network

  # Database
  mysql:
    image: mysql:8.0
    container_name: token-pedia-mysql
    ports:
      - "3306:3306"
    environment:
      MYSQL_ROOT_PASSWORD: password
      MYSQL_DATABASE: token_pedia
      MYSQL_USER: token_pedia
      MYSQL_PASSWORD: password
    volumes:
      - mysql_data:/var/lib/mysql
      - ./database/init:/docker-entrypoint-initdb.d
    networks:
      - token-pedia-network

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: token-pedia-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - token-pedia-network

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: token-pedia-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - backend
      - frontend
    networks:
      - token-pedia-network

volumes:
  mysql_data:
  redis_data:

networks:
  token-pedia-network:
    driver: bridge
```

### **2. KUBERNETES CONFIGURATION**

#### **Backend Deployment:**
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: token-pedia-backend
  labels:
    app: token-pedia-backend
spec:
  replicas: 3
  selector:
    matchLabels:
      app: token-pedia-backend
  template:
    metadata:
      labels:
        app: token-pedia-backend
    spec:
      containers:
      - name: backend
        image: token-pedia/backend:latest
        ports:
        - containerPort: 9000
        env:
        - name: APP_ENV
          value: "production"
        - name: DB_HOST
          valueFrom:
            secretKeyRef:
              name: database-secret
              key: host
        - name: DB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: database-secret
              key: password
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 9000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /ready
            port: 9000
          initialDelaySeconds: 5
          periodSeconds: 5

---
apiVersion: v1
kind: Service
metadata:
  name: token-pedia-backend-service
spec:
  selector:
    app: token-pedia-backend
  ports:
    - protocol: TCP
      port: 80
      targetPort: 9000
  type: ClusterIP
```

#### **Frontend Deployment:**
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: token-pedia-frontend
  labels:
    app: token-pedia-frontend
spec:
  replicas: 2
  selector:
    matchLabels:
      app: token-pedia-frontend
  template:
    metadata:
      labels:
        app: token-pedia-frontend
    spec:
      containers:
      - name: frontend
        image: token-pedia/frontend:latest
        ports:
        - containerPort: 80
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "256Mi"
            cpu: "200m"

---
apiVersion: v1
kind: Service
metadata:
  name: token-pedia-frontend-service
spec:
  selector:
    app: token-pedia-frontend
  ports:
    - protocol: TCP
      port: 80
      targetPort: 80
  type: LoadBalancer
```

### **3. TERRAFORM INFRASTRUCTURE**

#### **AWS Infrastructure:**
```hcl
# main.tf
terraform {
  required_version = ">= 1.0"
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
  }
}

provider "aws" {
  region = var.aws_region
}

# VPC
resource "aws_vpc" "token_pedia_vpc" {
  cidr_block           = "10.0.0.0/16"
  enable_dns_hostnames = true
  enable_dns_support   = true

  tags = {
    Name        = "token-pedia-vpc"
    Environment = var.environment
  }
}

# Internet Gateway
resource "aws_internet_gateway" "token_pedia_igw" {
  vpc_id = aws_vpc.token_pedia_vpc.id

  tags = {
    Name        = "token-pedia-igw"
    Environment = var.environment
  }
}

# Public Subnets
resource "aws_subnet" "public_subnets" {
  count             = length(var.public_subnet_cidrs)
  vpc_id            = aws_vpc.token_pedia_vpc.id
  cidr_block        = var.public_subnet_cidrs[count.index]
  availability_zone = var.availability_zones[count.index]

  map_public_ip_on_launch = true

  tags = {
    Name        = "public-subnet-${count.index + 1}"
    Environment = var.environment
  }
}

# Private Subnets
resource "aws_subnet" "private_subnets" {
  count             = length(var.private_subnet_cidrs)
  vpc_id            = aws_vpc.token_pedia_vpc.id
  cidr_block        = var.private_subnet_cidrs[count.index]
  availability_zone = var.availability_zones[count.index]

  tags = {
    Name        = "private-subnet-${count.index + 1}"
    Environment = var.environment
  }
}

# RDS Database
resource "aws_db_instance" "token_pedia_db" {
  identifier     = "token-pedia-${var.environment}"
  engine         = "mysql"
  engine_version = "8.0"
  instance_class = var.db_instance_class
  
  allocated_storage     = 20
  max_allocated_storage = 100
  storage_type          = "gp2"
  storage_encrypted     = true

  db_name  = "token_pedia"
  username = var.db_username
  password = var.db_password

  vpc_security_group_ids = [aws_security_group.rds_sg.id]
  db_subnet_group_name   = aws_db_subnet_group.token_pedia_db_subnet_group.name

  backup_retention_period = 7
  backup_window          = "03:00-04:00"
  maintenance_window     = "sun:04:00-sun:05:00"

  skip_final_snapshot = var.environment == "development"
  deletion_protection = var.environment == "production"

  tags = {
    Name        = "token-pedia-db"
    Environment = var.environment
  }
}

# ElastiCache Redis
resource "aws_elasticache_subnet_group" "token_pedia_cache_subnet" {
  name       = "token-pedia-cache-subnet"
  subnet_ids = aws_subnet.private_subnets[*].id
}

resource "aws_elasticache_cluster" "token_pedia_redis" {
  cluster_id           = "token-pedia-redis"
  engine               = "redis"
  node_type            = var.redis_node_type
  num_cache_nodes      = 1
  parameter_group_name = "default.redis7"
  port                 = 6379
  subnet_group_name    = aws_elasticache_subnet_group.token_pedia_cache_subnet.name
  security_group_ids   = [aws_security_group.redis_sg.id]

  tags = {
    Name        = "token-pedia-redis"
    Environment = var.environment
  }
}

# ECS Cluster
resource "aws_ecs_cluster" "token_pedia_cluster" {
  name = "token-pedia-${var.environment}"

  setting {
    name  = "containerInsights"
    value = "enabled"
  }

  tags = {
    Name        = "token-pedia-cluster"
    Environment = var.environment
  }
}

# Application Load Balancer
resource "aws_lb" "token_pedia_alb" {
  name               = "token-pedia-alb"
  internal           = false
  load_balancer_type = "application"
  security_groups    = [aws_security_group.alb_sg.id]
  subnets            = aws_subnet.public_subnets[*].id

  enable_deletion_protection = var.environment == "production"

  tags = {
    Name        = "token-pedia-alb"
    Environment = var.environment
  }
}

# S3 Bucket for static assets
resource "aws_s3_bucket" "token_pedia_assets" {
  bucket = "token-pedia-assets-${var.environment}-${random_string.bucket_suffix.result}"

  tags = {
    Name        = "token-pedia-assets"
    Environment = var.environment
  }
}

resource "aws_s3_bucket_public_access_block" "token_pedia_assets_pab" {
  bucket = aws_s3_bucket.token_pedia_assets.id

  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}

# CloudFront Distribution
resource "aws_cloudfront_distribution" "token_pedia_cdn" {
  origin {
    domain_name = aws_s3_bucket.token_pedia_assets.bucket_regional_domain_name
    origin_id   = "S3-${aws_s3_bucket.token_pedia_assets.id}"

    s3_origin_config {
      origin_access_identity = aws_cloudfront_origin_access_identity.token_pedia_oai.cloudfront_access_identity_path
    }
  }

  enabled             = true
  is_ipv6_enabled     = true
  default_root_object = "index.html"

  default_cache_behavior {
    allowed_methods        = ["DELETE", "GET", "HEAD", "OPTIONS", "PATCH", "POST", "PUT"]
    cached_methods         = ["GET", "HEAD"]
    target_origin_id       = "S3-${aws_s3_bucket.token_pedia_assets.id}"
    compress               = true
    viewer_protocol_policy = "redirect-to-https"

    forwarded_values {
      query_string = false
      cookies {
        forward = "none"
      }
    }

    min_ttl     = 0
    default_ttl = 3600
    max_ttl     = 86400
  }

  restrictions {
    geo_restriction {
      restriction_type = "none"
    }
  }

  viewer_certificate {
    cloudfront_default_certificate = true
  }

  tags = {
    Name        = "token-pedia-cdn"
    Environment = var.environment
  }
}

# Random string for unique bucket naming
resource "random_string" "bucket_suffix" {
  length  = 8
  special = false
  upper   = false
}
```

---

## 🔄 CI/CD PIPELINE

### **GitHub Actions Workflow:**

#### **Main CI/CD Pipeline:**
```yaml
name: TOKEN PEDIA CI/CD Pipeline

on:
  push:
    branches: [main, develop, staging]
  pull_request:
    branches: [main, develop]

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  # Code Quality & Security
  code-quality:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: 8.1
          extensions: mbstring, xml, ctype, iconv, intl, pdo_mysql

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 18
          cache: 'npm'

      - name: Install Backend Dependencies
        working-directory: ./backend
        run: composer install --no-interaction --prefer-dist

      - name: Install Frontend Dependencies
        working-directory: ./frontend
        run: npm ci

      - name: Run PHP Code Style Check
        working-directory: ./backend
        run: vendor/bin/pint --test

      - name: Run PHP Static Analysis
        working-directory: ./backend
        run: vendor/bin/phpstan analyse

      - name: Run Frontend Linting
        working-directory: ./frontend
        run: npm run lint

      - name: Run Security Audit
        working-directory: ./backend
        run: composer audit

      - name: Run Frontend Security Audit
        working-directory: ./frontend
        run: npm audit --audit-level=high

  # Backend Tests
  backend-tests:
    runs-on: ubuntu-latest
    needs: code-quality
    
    services:
      mysql:
        image: mysql:8.0
        env:
          MYSQL_ROOT_PASSWORD: password
          MYSQL_DATABASE: token_pedia_test
        ports:
          - 3306:3306
        options: --health-cmd="mysqladmin ping" --health-interval=10s --health-timeout=5s --health-retries=3

      redis:
        image: redis:7-alpine
        ports:
          - 6379:6379
        options: --health-cmd="redis-cli ping" --health-interval=10s --health-timeout=5s --health-retries=3

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: 8.1
          extensions: mbstring, xml, ctype, iconv, intl, pdo_mysql
          coverage: xdebug

      - name: Install Dependencies
        working-directory: ./backend
        run: composer install --no-interaction --prefer-dist

      - name: Copy Environment File
        working-directory: ./backend
        run: cp .env.testing .env

      - name: Generate Application Key
        working-directory: ./backend
        run: php artisan key:generate

      - name: Run Database Migrations
        working-directory: ./backend
        run: php artisan migrate --force

      - name: Run Unit Tests
        working-directory: ./backend
        run: php artisan test --testsuite=Unit --coverage-clover=coverage.xml

      - name: Run Feature Tests
        working-directory: ./backend
        run: php artisan test --testsuite=Feature

      - name: Upload Coverage to Codecov
        uses: codecov/codecov-action@v3
        with:
          file: ./backend/coverage.xml
          flags: backend

  # Frontend Tests
  frontend-tests:
    runs-on: ubuntu-latest
    needs: code-quality

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 18
          cache: 'npm'

      - name: Install Dependencies
        working-directory: ./frontend
        run: npm ci

      - name: Run Type Check
        working-directory: ./frontend
        run: npm run type-check

      - name: Run Unit Tests
        working-directory: ./frontend
        run: npm run test:coverage

      - name: Run E2E Tests
        working-directory: ./frontend
        run: npm run test:e2e

      - name: Upload Coverage to Codecov
        uses: codecov/codecov-action@v3
        with:
          file: ./frontend/coverage/lcov.info
          flags: frontend

  # Build and Push Docker Images
  build-and-push:
    runs-on: ubuntu-latest
    needs: [backend-tests, frontend-tests]
    if: github.ref == 'refs/heads/main' || github.ref == 'refs/heads/staging'

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Log in to Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Extract metadata
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
          tags: |
            type=ref,event=branch
            type=sha,prefix={{branch}}-

      - name: Build and push Backend image
        uses: docker/build-push-action@v5
        with:
          context: ./backend
          push: true
          tags: ${{ steps.meta.outputs.tags }}-backend
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max

      - name: Build and push Frontend image
        uses: docker/build-push-action@v5
        with:
          context: ./frontend
          push: true
          tags: ${{ steps.meta.outputs.tags }}-frontend
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max

  # Deploy to Staging
  deploy-staging:
    runs-on: ubuntu-latest
    needs: build-and-push
    if: github.ref == 'refs/heads/staging'
    environment: staging

    steps:
      - name: Deploy to Staging
        run: |
          echo "Deploying to staging environment..."
          # Add deployment scripts here

  # Deploy to Production
  deploy-production:
    runs-on: ubuntu-latest
    needs: build-and-push
    if: github.ref == 'refs/heads/main'
    environment: production

    steps:
      - name: Deploy to Production
        run: |
          echo "Deploying to production environment..."
          # Add deployment scripts here
```

---

## 📊 MONITORING & ALERTING

### **1. PROMETHEUS CONFIGURATION**

#### **Prometheus Config:**
```yaml
# prometheus.yml
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "alert_rules.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

scrape_configs:
  - job_name: 'token-pedia-backend'
    static_configs:
      - targets: ['backend:9000']
    metrics_path: /metrics
    scrape_interval: 30s

  - job_name: 'token-pedia-frontend'
    static_configs:
      - targets: ['frontend:80']
    metrics_path: /metrics
    scrape_interval: 30s

  - job_name: 'mysql'
    static_configs:
      - targets: ['mysql-exporter:9104']

  - job_name: 'redis'
    static_configs:
      - targets: ['redis-exporter:9121']

  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']
```

#### **Alert Rules:**
```yaml
# alert_rules.yml
groups:
  - name: token-pedia-alerts
    rules:
      - alert: HighErrorRate
        expr: rate(http_requests_total{status=~"5.."}[5m]) > 0.1
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "High error rate detected"
          description: "Error rate is {{ $value }} errors per second"

      - alert: HighResponseTime
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 2
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High response time detected"
          description: "95th percentile response time is {{ $value }} seconds"

      - alert: DatabaseConnectionFailure
        expr: mysql_up == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Database connection failure"
          description: "MySQL database is down"

      - alert: RedisConnectionFailure
        expr: redis_up == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Redis connection failure"
          description: "Redis cache is down"

      - alert: HighCPUUsage
        expr: 100 - (avg by(instance) (rate(node_cpu_seconds_total{mode="idle"}[5m])) * 100) > 80
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High CPU usage detected"
          description: "CPU usage is {{ $value }}%"

      - alert: HighMemoryUsage
        expr: (1 - (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes)) * 100 > 85
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High memory usage detected"
          description: "Memory usage is {{ $value }}%"

      - alert: DiskSpaceLow
        expr: (1 - (node_filesystem_avail_bytes / node_filesystem_size_bytes)) * 100 > 90
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "Disk space low"
          description: "Disk usage is {{ $value }}%"
```

### **2. GRAFANA DASHBOARDS**

#### **Application Dashboard:**
```json
{
  "dashboard": {
    "title": "TOKEN PEDIA - Application Metrics",
    "panels": [
      {
        "title": "Request Rate",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(http_requests_total[5m])",
            "legendFormat": "{{method}} {{status}}"
          }
        ]
      },
      {
        "title": "Response Time",
        "type": "graph",
        "targets": [
          {
            "expr": "histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m]))",
            "legendFormat": "95th percentile"
          },
          {
            "expr": "histogram_quantile(0.50, rate(http_request_duration_seconds_bucket[5m]))",
            "legendFormat": "50th percentile"
          }
        ]
      },
      {
        "title": "Error Rate",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(http_requests_total{status=~\"5..\"}[5m])",
            "legendFormat": "5xx errors"
          },
          {
            "expr": "rate(http_requests_total{status=~\"4..\"}[5m])",
            "legendFormat": "4xx errors"
          }
        ]
      },
      {
        "title": "Database Connections",
        "type": "graph",
        "targets": [
          {
            "expr": "mysql_global_status_threads_connected",
            "legendFormat": "Active connections"
          }
        ]
      }
    ]
  }
}
```

### **3. LOGGING CONFIGURATION**

#### **ELK Stack Docker Compose:**
```yaml
version: '3.8'

services:
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.8.0
    container_name: elasticsearch
    environment:
      - discovery.type=single-node
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
      - xpack.security.enabled=false
    ports:
      - "9200:9200"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data

  logstash:
    image: docker.elastic.co/logstash/logstash:8.8.0
    container_name: logstash
    ports:
      - "5044:5044"
      - "9600:9600"
    volumes:
      - ./logstash/pipeline:/usr/share/logstash/pipeline
      - ./logstash/config:/usr/share/logstash/config
    depends_on:
      - elasticsearch

  kibana:
    image: docker.elastic.co/kibana/kibana:8.8.0
    container_name: kibana
    ports:
      - "5601:5601"
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
    depends_on:
      - elasticsearch

volumes:
  elasticsearch_data:
```

#### **Logstash Pipeline:**
```ruby
# logstash/pipeline/logstash.conf
input {
  beats {
    port => 5044
  }
}

filter {
  if [fields][service] == "token-pedia-backend" {
    grok {
      match => { "message" => "%{TIMESTAMP_ISO8601:timestamp} %{LOGLEVEL:level} %{GREEDYDATA:message}" }
    }

    date {
      match => [ "timestamp", "ISO8601" ]
    }

    if [level] == "ERROR" {
      mutate {
        add_tag => [ "error" ]
      }
    }
  }

  if [fields][service] == "token-pedia-frontend" {
    json {
      source => "message"
    }
  }
}

output {
  elasticsearch {
    hosts => ["elasticsearch:9200"]
    index => "token-pedia-%{+YYYY.MM.dd}"
  }

  if "error" in [tags] {
    email {
      to => "<EMAIL>"
      subject => "TOKEN PEDIA Error Alert"
      body => "Error detected: %{message}"
    }
  }
}
```

---

## 🔧 DEPLOYMENT AUTOMATION

### **1. BLUE-GREEN DEPLOYMENT SCRIPT**

#### **Deployment Script:**
```bash
#!/bin/bash

# Blue-Green Deployment Script for TOKEN PEDIA
set -e

# Configuration
ENVIRONMENT=${1:-staging}
NEW_VERSION=${2:-latest}
HEALTH_CHECK_URL="https://api.tokenpedia.com/health"
ROLLBACK_ON_FAILURE=${3:-true}

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
}

# Pre-deployment checks
pre_deployment_checks() {
    log "Running pre-deployment checks..."

    # Check if new version exists
    if ! docker image inspect "token-pedia/backend:${NEW_VERSION}" > /dev/null 2>&1; then
        error "Backend image token-pedia/backend:${NEW_VERSION} not found"
        exit 1
    fi

    if ! docker image inspect "token-pedia/frontend:${NEW_VERSION}" > /dev/null 2>&1; then
        error "Frontend image token-pedia/frontend:${NEW_VERSION} not found"
        exit 1
    fi

    # Check database connectivity
    if ! docker exec token-pedia-mysql mysqladmin ping -h localhost --silent; then
        error "Database is not accessible"
        exit 1
    fi

    log "Pre-deployment checks passed"
}

# Deploy to green environment
deploy_green() {
    log "Deploying to green environment..."

    # Stop existing green containers if any
    docker-compose -f docker-compose.green.yml down || true

    # Update docker-compose file with new version
    sed -i "s/image: token-pedia\/backend:.*/image: token-pedia\/backend:${NEW_VERSION}/" docker-compose.green.yml
    sed -i "s/image: token-pedia\/frontend:.*/image: token-pedia\/frontend:${NEW_VERSION}/" docker-compose.green.yml

    # Start green environment
    docker-compose -f docker-compose.green.yml up -d

    # Wait for services to be ready
    log "Waiting for services to be ready..."
    sleep 30

    # Run database migrations
    docker-compose -f docker-compose.green.yml exec -T backend php artisan migrate --force

    log "Green environment deployed successfully"
}

# Health check
health_check() {
    log "Performing health checks..."

    local max_attempts=30
    local attempt=1

    while [ $attempt -le $max_attempts ]; do
        if curl -f -s "${HEALTH_CHECK_URL}" > /dev/null; then
            log "Health check passed"
            return 0
        fi

        warn "Health check failed (attempt $attempt/$max_attempts)"
        sleep 10
        ((attempt++))
    done

    error "Health check failed after $max_attempts attempts"
    return 1
}

# Switch traffic to green
switch_traffic() {
    log "Switching traffic to green environment..."

    # Update load balancer configuration
    # This would typically involve updating nginx config or cloud load balancer

    # For this example, we'll swap the docker-compose files
    cp docker-compose.yml docker-compose.blue.yml
    cp docker-compose.green.yml docker-compose.yml

    # Reload nginx configuration
    docker exec token-pedia-nginx nginx -s reload

    log "Traffic switched to green environment"
}

# Cleanup old environment
cleanup_blue() {
    log "Cleaning up blue environment..."

    # Stop blue environment
    docker-compose -f docker-compose.blue.yml down

    # Remove old images (keep last 3 versions)
    docker images token-pedia/backend --format "table {{.Tag}}" | tail -n +4 | xargs -r docker rmi token-pedia/backend: || true
    docker images token-pedia/frontend --format "table {{.Tag}}" | tail -n +4 | xargs -r docker rmi token-pedia/frontend: || true

    log "Blue environment cleaned up"
}

# Rollback function
rollback() {
    error "Deployment failed, rolling back..."

    # Switch back to blue environment
    cp docker-compose.blue.yml docker-compose.yml
    docker exec token-pedia-nginx nginx -s reload

    # Start blue environment if not running
    docker-compose up -d

    # Stop green environment
    docker-compose -f docker-compose.green.yml down

    log "Rollback completed"
    exit 1
}

# Main deployment process
main() {
    log "Starting blue-green deployment for TOKEN PEDIA"
    log "Environment: $ENVIRONMENT"
    log "Version: $NEW_VERSION"

    # Set trap for rollback on failure
    if [ "$ROLLBACK_ON_FAILURE" = "true" ]; then
        trap rollback ERR
    fi

    pre_deployment_checks
    deploy_green

    if health_check; then
        switch_traffic
        sleep 60  # Wait a bit to ensure everything is stable

        if health_check; then
            cleanup_blue
            log "Deployment completed successfully!"
        else
            rollback
        fi
    else
        rollback
    fi
}

# Run main function
main "$@"
```

### **2. AUTOMATED BACKUP SCRIPT**

#### **Database Backup Script:**
```bash
#!/bin/bash

# Automated Database Backup Script
set -e

# Configuration
BACKUP_DIR="/backups/mysql"
RETENTION_DAYS=30
DB_HOST="localhost"
DB_USER="backup_user"
DB_PASSWORD="${DB_PASSWORD}"
DB_NAME="token_pedia"
S3_BUCKET="token-pedia-backups"

# Create backup directory
mkdir -p "$BACKUP_DIR"

# Generate backup filename
BACKUP_FILE="token_pedia_$(date +%Y%m%d_%H%M%S).sql.gz"
BACKUP_PATH="$BACKUP_DIR/$BACKUP_FILE"

log() {
    echo "[$(date +'%Y-%m-%d %H:%M:%S')] $1"
}

# Create database backup
create_backup() {
    log "Creating database backup..."

    mysqldump \
        --host="$DB_HOST" \
        --user="$DB_USER" \
        --password="$DB_PASSWORD" \
        --single-transaction \
        --routines \
        --triggers \
        --events \
        --hex-blob \
        "$DB_NAME" | gzip > "$BACKUP_PATH"

    log "Backup created: $BACKUP_PATH"
}

# Upload to S3
upload_to_s3() {
    log "Uploading backup to S3..."

    aws s3 cp "$BACKUP_PATH" "s3://$S3_BUCKET/mysql/" \
        --storage-class STANDARD_IA

    log "Backup uploaded to S3"
}

# Cleanup old backups
cleanup_old_backups() {
    log "Cleaning up old backups..."

    # Local cleanup
    find "$BACKUP_DIR" -name "*.sql.gz" -mtime +$RETENTION_DAYS -delete

    # S3 cleanup (using lifecycle policy is recommended)
    aws s3 ls "s3://$S3_BUCKET/mysql/" --recursive | \
        awk '$1 < "'$(date -d "$RETENTION_DAYS days ago" '+%Y-%m-%d')'" {print $4}' | \
        xargs -I {} aws s3 rm "s3://$S3_BUCKET/{}"

    log "Old backups cleaned up"
}

# Verify backup
verify_backup() {
    log "Verifying backup integrity..."

    if gzip -t "$BACKUP_PATH"; then
        log "Backup verification successful"
    else
        log "ERROR: Backup verification failed"
        exit 1
    fi
}

# Main backup process
main() {
    log "Starting automated backup process"

    create_backup
    verify_backup
    upload_to_s3
    cleanup_old_backups

    log "Backup process completed successfully"
}

# Run main function
main "$@"
```

### **3. MONITORING SETUP SCRIPT**

#### **Monitoring Stack Setup:**
```bash
#!/bin/bash

# Monitoring Stack Setup Script
set -e

MONITORING_DIR="/opt/monitoring"
GRAFANA_ADMIN_PASSWORD="${GRAFANA_ADMIN_PASSWORD:-admin123}"

log() {
    echo "[$(date +'%Y-%m-%d %H:%M:%S')] $1"
}

setup_directories() {
    log "Setting up monitoring directories..."

    mkdir -p "$MONITORING_DIR"/{prometheus,grafana,alertmanager}
    mkdir -p "$MONITORING_DIR"/grafana/{dashboards,provisioning}

    log "Directories created"
}

setup_prometheus() {
    log "Setting up Prometheus configuration..."

    cat > "$MONITORING_DIR/prometheus/prometheus.yml" << 'EOF'
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "alert_rules.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

scrape_configs:
  - job_name: 'token-pedia-backend'
    static_configs:
      - targets: ['backend:9000']
    metrics_path: /metrics
    scrape_interval: 30s

  - job_name: 'token-pedia-frontend'
    static_configs:
      - targets: ['frontend:80']
    metrics_path: /metrics
    scrape_interval: 30s
EOF

    log "Prometheus configuration created"
}

setup_grafana() {
    log "Setting up Grafana configuration..."

    # Create datasource configuration
    cat > "$MONITORING_DIR/grafana/provisioning/datasources.yml" << 'EOF'
apiVersion: 1

datasources:
  - name: Prometheus
    type: prometheus
    access: proxy
    url: http://prometheus:9090
    isDefault: true
EOF

    # Create dashboard configuration
    cat > "$MONITORING_DIR/grafana/provisioning/dashboards.yml" << 'EOF'
apiVersion: 1

providers:
  - name: 'default'
    orgId: 1
    folder: ''
    type: file
    disableDeletion: false
    updateIntervalSeconds: 10
    options:
      path: /var/lib/grafana/dashboards
EOF

    log "Grafana configuration created"
}

start_monitoring_stack() {
    log "Starting monitoring stack..."

    cat > "$MONITORING_DIR/docker-compose.monitoring.yml" << EOF
version: '3.8'

services:
  prometheus:
    image: prom/prometheus:latest
    container_name: prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus:/etc/prometheus
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--web.enable-lifecycle'

  grafana:
    image: grafana/grafana:latest
    container_name: grafana
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=$GRAFANA_ADMIN_PASSWORD
    volumes:
      - grafana_data:/var/lib/grafana
      - ./grafana/provisioning:/etc/grafana/provisioning
      - ./grafana/dashboards:/var/lib/grafana/dashboards

  alertmanager:
    image: prom/alertmanager:latest
    container_name: alertmanager
    ports:
      - "9093:9093"
    volumes:
      - ./alertmanager:/etc/alertmanager

volumes:
  prometheus_data:
  grafana_data:
EOF

    cd "$MONITORING_DIR"
    docker-compose -f docker-compose.monitoring.yml up -d

    log "Monitoring stack started successfully"
}

main() {
    log "Setting up TOKEN PEDIA monitoring stack"

    setup_directories
    setup_prometheus
    setup_grafana
    start_monitoring_stack

    log "Monitoring setup completed!"
    log "Grafana: http://localhost:3001 (admin/$GRAFANA_ADMIN_PASSWORD)"
    log "Prometheus: http://localhost:9090"
    log "Alertmanager: http://localhost:9093"
}

main "$@"
```

---

**DevOps automation ini memastikan deployment yang aman, scalable, dan dapat dimonitor untuk TOKEN PEDIA dengan zero-downtime deployment strategy, comprehensive monitoring, dan automated backup systems.**
