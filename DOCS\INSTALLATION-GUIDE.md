# 🚀 SantriMental Module Installation Guide

## 📋 **Overview**

SantriMental adalah platform asesmen kesehatan mental yang dapat diinstal sebagai modul terpisah pada fresh Laravel installation. Guide ini akan membantu Anda menginstal modul-modul SantriMental dari awal.

## 🎯 **Metode Instalasi**

### **Metode 1: Automated Installer (Recommended)**

#### **Step 1: Extract Modules**
```bash
# Jalankan script ekstraksi
extract-modules.bat

# Buat installer package
create-installer.bat
```

#### **Step 2: Fresh Laravel Installation**
```bash
# Buat project Laravel baru
composer create-project laravel/laravel santrimental-fresh
cd santrimental-fresh

# Copy installer ke project root
# Extract santrimental-installer.zip ke folder ini

# Jalankan installer
install.bat
```

#### **Step 3: Setup Database**
```bash
# Update .env file dengan database config
# Jalankan migrasi
php artisan migrate
php artisan db:seed

# Start server
php artisan serve
```

### **Metode 2: Composer Package**

#### **Step 1: Create Package**
```bash
# Buat composer package
create-composer-package.bat
```

#### **Step 2: Install via Composer**
```bash
# Fresh Laravel
composer create-project laravel/laravel santrimental-fresh
cd santrimental-fresh

# Install package (local)
composer config repositories.local path ../santrimental-package
composer require santrimental/core

# Install modules
php artisan santrimental:install
php artisan migrate
php artisan db:seed
```

### **Metode 3: Manual Installation**

#### **Step 1: Fresh Laravel Setup**
```bash
composer create-project laravel/laravel santrimental-fresh
cd santrimental-fresh
```

#### **Step 2: Copy Files Manually**

**Frontend Files:**
```bash
# Copy views
copy santrimental-modules/resources/views/* resources/views/

# Copy assets
copy santrimental-modules/public/js/* public/js/
copy santrimental-modules/public/css/* public/css/
```

**Backend Files:**
```bash
# Copy controllers
copy santrimental-modules/app/Http/Controllers/* app/Http/Controllers/

# Copy models
copy santrimental-modules/app/Models/* app/Models/

# Copy migrations
copy santrimental-modules/database/migrations/* database/migrations/

# Copy seeders
copy santrimental-modules/database/seeders/* database/seeders/

# Copy routes
copy santrimental-modules/routes/web.php routes/web.php
copy santrimental-modules/routes/api.php routes/api.php

# Copy middleware
copy santrimental-modules/app/Http/Middleware/* app/Http/Middleware/
```

#### **Step 3: Configuration**

**Update .env:**
```env
APP_NAME=SantriMental
APP_URL=http://localhost:8000

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=santrimental
DB_USERNAME=root
DB_PASSWORD=

# SantriMental specific
FRONTEND_URL=http://localhost:8000
SESSION_DOMAIN=localhost
SANCTUM_STATEFUL_DOMAINS=localhost
```

**Register Middleware (app/Http/Kernel.php):**
```php
protected $routeMiddleware = [
    // ... existing middleware
    'role' => \App\Http\Middleware\RoleMiddleware::class,
];
```

#### **Step 4: Database Setup**
```bash
php artisan migrate
php artisan db:seed
```

## 📁 **Module Structure**

### **Frontend Modules**
```
resources/views/
├── index.blade.php              # Landing page
├── dashboard.blade.php          # Student dashboard
├── admin-dashboard.blade.php    # Admin dashboard
├── guru-dashboard.blade.php     # Teacher dashboard
├── orangtua-dashboard.blade.php # Parent dashboard
├── assessments.blade.php        # Assessment selection
├── history.blade.php           # Assessment history
├── dynamic-form.blade.php      # Universal form
└── srq20-form.blade.php        # SRQ-20 assessment

public/js/
├── auth.js                     # Authentication
├── dashboard.js                # Dashboard functionality
├── dynamic-form.js             # Form rendering
├── history.js                  # History management
├── modern-components.js        # UI components
├── modern-dashboard.js         # Dashboard features
├── performance-optimizer.js    # Performance
├── srq20-form.js              # SRQ-20 specific
└── utils.js                   # Utilities

public/css/
└── modern-dashboard.css       # Complete design system
```

### **Backend Modules**
```
app/Http/Controllers/Api/
├── AuthController.php         # Authentication API
├── AssessmentController.php   # Assessment management
├── FormTemplateController.php # Dynamic forms
└── RoleController.php         # Role management

app/Models/
├── User.php                   # Enhanced user model
├── Assessment.php             # Assessment model
├── AssessmentAnswer.php       # Assessment answers
├── FormTemplate.php           # Form templates
├── FormQuestion.php           # Form questions
├── FormResponse.php           # Form responses
├── Role.php                   # Role model
└── StudentParentRelationship.php # Relationships

database/migrations/
├── *_create_assessments_table.php
├── *_create_assessment_answers_table.php
├── *_create_form_templates_table.php
├── *_create_roles_table.php
├── *_create_student_parent_relationships_table.php
├── *_add_additional_fields_to_users_table.php
├── *_create_assessment_forms_table.php
└── *_create_assessment_responses_table.php

database/seeders/
├── RoleSeeder.php             # Role data
├── UserSeeder.php             # Sample users
├── FormTemplateSeeder.php     # Form templates
├── AssessmentFormSeeder.php   # Assessment forms
└── StudentParentRelationshipSeeder.php # Relationships
```

## 🔧 **Configuration Requirements**

### **PHP Extensions**
- PHP >= 8.1
- BCMath PHP Extension
- Ctype PHP Extension
- Fileinfo PHP Extension
- JSON PHP Extension
- Mbstring PHP Extension
- OpenSSL PHP Extension
- PDO PHP Extension
- Tokenizer PHP Extension
- XML PHP Extension

### **Database**
- MySQL >= 8.0 atau PostgreSQL >= 12

### **Web Server**
- Apache dengan mod_rewrite atau Nginx

## 🎯 **Post-Installation**

### **URLs yang Tersedia**
- **Home**: `http://localhost:8000`
- **Student Dashboard**: `http://localhost:8000/dashboard`
- **Admin Dashboard**: `http://localhost:8000/admin/dashboard`
- **Teacher Dashboard**: `http://localhost:8000/guru/dashboard`
- **Parent Dashboard**: `http://localhost:8000/orangtua/dashboard`
- **Assessments**: `http://localhost:8000/assessments`
- **History**: `http://localhost:8000/history`

### **Default Users (dari seeder)**
- **Admin**: <EMAIL> / password
- **Guru**: <EMAIL> / password
- **Siswa**: <EMAIL> / password
- **Orang Tua**: <EMAIL> / password

### **Features yang Aktif**
- ✅ Multi-role authentication
- ✅ Dynamic form system
- ✅ Mental health assessments (SRQ-20)
- ✅ Modern responsive UI
- ✅ Dark/Light theme
- ✅ Role-based dashboards
- ✅ Assessment history
- ✅ Parent-student relationships

## 🔍 **Troubleshooting**

### **Common Issues**

**1. Permission Errors**
```bash
# Fix storage permissions
chmod -R 775 storage bootstrap/cache
```

**2. Database Connection**
```bash
# Check .env database configuration
# Ensure database exists
# Test connection: php artisan migrate:status
```

**3. Asset Loading Issues**
```bash
# Clear cache
php artisan cache:clear
php artisan config:clear
php artisan route:clear
php artisan view:clear
```

**4. JavaScript Errors**
```bash
# Check browser console
# Ensure all JS files are loaded
# Verify asset paths in views
```

### **Verification Steps**

1. **Check Routes**: `php artisan route:list`
2. **Check Database**: `php artisan migrate:status`
3. **Check Seeders**: Verify users exist in database
4. **Check Assets**: Verify CSS/JS files in public folder
5. **Check Permissions**: Ensure storage is writable

## 📞 **Support**

Jika mengalami masalah:
1. Check error logs di `storage/logs/laravel.log`
2. Verify semua files ter-copy dengan benar
3. Pastikan database configuration benar
4. Check browser console untuk JavaScript errors

## 🎉 **Success Indicators**

Installation berhasil jika:
- ✅ Semua URLs dapat diakses
- ✅ Dashboard menampilkan data mock
- ✅ Modern UI ter-load dengan benar
- ✅ No errors di browser console
- ✅ Database tables ter-create
- ✅ Sample users dapat login

Selamat! SantriMental modules berhasil diinstal pada fresh Laravel installation! 🚀
